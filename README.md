# YScript 个人资源仓库

这是一个个人资源收集仓库，包含了音乐、图片、配置文件等各种资源。

## 📁 仓库结构

```
yscript/
├── README.md           # 项目说明文档
├── music/              # 音乐文件目录
├── pics/               # 图片资源目录
│   ├── BingWallpaper.jpg
│   └── database_validate_phase.png
└── vim/                # Vim 配置文件
    ├── .vimrc          # Vim 配置文件
    ├── .bashrc         # Bash 配置文件
    ├── vim.tar         # Vim 配置打包文件
    └── !               # 备份配置文件
```

## ⚙️ 配置文件 (vim/)

个人开发环境配置文件：
- **.vimrc** - Vim 编辑器配置，包含插件管理、快捷键设置、代码高亮等
- **.bashrc** - Bash shell 配置，包含别名、环境变量、提示符设置等
- **vim.tar** - Vim 配置的打包备份
- **!** - 配置文件备份

### Vim 配置特性

- 使用 vim-plug 插件管理器
- 集成 Git 操作 (vim-fugitive)
- 代码搜索和导航 (cscope, grep)
- 语法高亮和智能缩进
- 自定义快捷键映射

### Bash 配置特性

- 彩色提示符和 Git 分支显示
- 常用命令别名
- 历史记录优化
- 开发工具路径配置

## 📝 使用说明

这是一个个人资源仓库，主要用于：
1. 备份和同步个人配置文件
2. 收集和整理各种资源文件
3. 版本控制个人开发环境设置

## 🔧 环境要求

- Linux/Unix 系统
- Vim 编辑器
- Git 版本控制
- Bash shell

---

*最后更新：2024年*